// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		0909D9D3F3E8A4E1A0334C6D /* Pods_Imagine.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8D129A29723C0FF96CB93692 /* Pods_Imagine.framework */; };
		7E3453FB2921F395001276CF /* PGTabbarController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E3453FA2921F395001276CF /* PGTabbarController.swift */; };
		7E345420292240DF001276CF /* LoginManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E34541F292240DF001276CF /* LoginManager.swift */; };
		7E345422292323C9001276CF /* KeyChainManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E345421292323C9001276CF /* KeyChainManager.swift */; };
		7E34542529239FB8001276CF /* CIPromptView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E34542429239FB8001276CF /* CIPromptView.swift */; };
		7E34542729239FD2001276CF /* CIStyleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E34542629239FD2001276CF /* CIStyleView.swift */; };
		7E34542929239FF1001276CF /* CINumberView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E34542829239FF1001276CF /* CINumberView.swift */; };
		7E34542B2923A028001276CF /* CITitleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E34542A2923A028001276CF /* CITitleView.swift */; };
		7E34542E2924B7E0001276CF /* UIDefine.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E34542D2924B7E0001276CF /* UIDefine.swift */; };
		7E3454302924DD2C001276CF /* UIColor_extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E34542F2924DD2C001276CF /* UIColor_extension.swift */; };
		7E348C402941BA2B005BA9BE /* PhotoPickerViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E348C3B2941BA2B005BA9BE /* PhotoPickerViewModel.swift */; };
		7E348C412941BA2B005BA9BE /* PhotoPicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E348C3C2941BA2B005BA9BE /* PhotoPicker.swift */; };
		7E348C422941BA2B005BA9BE /* PhotoPickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E348C3E2941BA2B005BA9BE /* PhotoPickerView.swift */; };
		7E348C432941BA2B005BA9BE /* PhotoCellView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E348C3F2941BA2B005BA9BE /* PhotoCellView.swift */; };
		7E348C452941BB5A005BA9BE /* CICharacterVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E348C442941BB5A005BA9BE /* CICharacterVC.swift */; };
		7E348C472941D81E005BA9BE /* CICharacterListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E348C462941D81E005BA9BE /* CICharacterListView.swift */; };
		7E348C492941DC9E005BA9BE /* SwiftUIColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E348C482941DC9E005BA9BE /* SwiftUIColor.swift */; };
		7E348C4D2941E1B6005BA9BE /* CICharacterListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E348C4C2941E1B6005BA9BE /* CICharacterListCell.swift */; };
		7E348C55294707DD005BA9BE /* CICharacterModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E348C54294707DD005BA9BE /* CICharacterModel.swift */; };
		7E5536D3299117C7001CAD68 /* ContactsGuideVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E5536D2299117C7001CAD68 /* ContactsGuideVC.swift */; };
		7E5536D52992322B001CAD68 /* ContactsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E5536D42992322B001CAD68 /* ContactsCell.swift */; };
		7E5536D7299232A4001CAD68 /* Contact.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E5536D6299232A4001CAD68 /* Contact.swift */; };
		7E5536E32992716A001CAD68 /* ContactModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E5536E229927169001CAD68 /* ContactModel.swift */; };
		7E5536E6299273E8001CAD68 /* PersistentContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E5536E5299273E8001CAD68 /* PersistentContainer.swift */; };
		7E5536E929928C86001CAD68 /* imagine.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = 7E5536E729928C86001CAD68 /* imagine.xcdatamodeld */; };
		7E5536EB299A2E1D001CAD68 /* HomeNavigationBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E5536EA299A2E1D001CAD68 /* HomeNavigationBar.swift */; };
		7E5536ED299A5AF0001CAD68 /* UIImage_extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E5536EC299A5AF0001CAD68 /* UIImage_extension.swift */; };
		7E5536EF299DBB2E001CAD68 /* CommentVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E5536EE299DBB2E001CAD68 /* CommentVC.swift */; };
		7E5536F129A318BA001CAD68 /* CommentListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E5536F029A318BA001CAD68 /* CommentListView.swift */; };
		7E5536F329A31A10001CAD68 /* CommentCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E5536F229A31A10001CAD68 /* CommentCell.swift */; };
		7E5536F529A485A4001CAD68 /* SendView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E5536F429A485A4001CAD68 /* SendView.swift */; };
		7E5536F729A746FA001CAD68 /* CICharacterNameVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E5536F629A746FA001CAD68 /* CICharacterNameVC.swift */; };
		7E5536F929A74D36001CAD68 /* CICharacterLoadingVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E5536F829A74D36001CAD68 /* CICharacterLoadingVC.swift */; };
		7E5536FC29AC97D0001CAD68 /* ScribbleVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E5536FB29AC97D0001CAD68 /* ScribbleVC.swift */; };
		7E60D2AA2965738A00D48B1A /* String_extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E60D2A92965738A00D48B1A /* String_extension.swift */; };
		7E97A16D294886B800397AEA /* CIRandomPromptModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E97A16C294886B800397AEA /* CIRandomPromptModel.swift */; };
		7E97A16F2949C35600397AEA /* CICharacterNameModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E97A16E2949C35600397AEA /* CICharacterNameModel.swift */; };
		7E97A1712949C70B00397AEA /* CICharacterMaterialsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E97A1702949C70B00397AEA /* CICharacterMaterialsModel.swift */; };
		7E97A179295046A800397AEA /* LoginModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E97A178295046A800397AEA /* LoginModel.swift */; };
		7E97A17B29505A6500397AEA /* LoginWebVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E97A17A29505A6500397AEA /* LoginWebVC.swift */; };
		7EA892712951A84E00111D0D /* UIViewController_extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA892702951A84E00111D0D /* UIViewController_extension.swift */; };
		7EA892732951E1F800111D0D /* IGNavigationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA892722951E1F800111D0D /* IGNavigationViewController.swift */; };
		7EA892752951E2B400111D0D /* IGBaseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA892742951E2B400111D0D /* IGBaseViewController.swift */; };
		7EA89278295314F500111D0D /* FaceCropper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA89277295314F500111D0D /* FaceCropper.swift */; };
		7EA8927A295C0BA700111D0D /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 7EA89279295C0BA700111D0D /* GoogleService-Info.plist */; };
		7EA8927D295C2F0100111D0D /* CharacterVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA8927C295C2F0100111D0D /* CharacterVC.swift */; };
		7EA8927F295C30B200111D0D /* CharacterImagesVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA8927E295C30B200111D0D /* CharacterImagesVC.swift */; };
		7EA89282295C32EB00111D0D /* CharacterCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA89281295C32EB00111D0D /* CharacterCell.swift */; };
		7EA89284295C32FB00111D0D /* CharacterImagesCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA89283295C32FB00111D0D /* CharacterImagesCell.swift */; };
		7EA89286295C33A600111D0D /* CharacterListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA89285295C33A600111D0D /* CharacterListView.swift */; };
		7EA89288295C33D600111D0D /* CharacterImagesListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA89287295C33D600111D0D /* CharacterImagesListView.swift */; };
		7EA8928B295D835300111D0D /* CharacterManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA8928A295D835300111D0D /* CharacterManager.swift */; };
		7EA8928D295D83A800111D0D /* CharacterListModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA8928C295D83A800111D0D /* CharacterListModel.swift */; };
		7EA8928F295D83B400111D0D /* CharacterInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA8928E295D83B400111D0D /* CharacterInfoModel.swift */; };
		7EA89291295D84F100111D0D /* CharacterListParam.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA89290295D84F100111D0D /* CharacterListParam.swift */; };
		7EA89293295D85B600111D0D /* CharacterInfoParam.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA89292295D85B600111D0D /* CharacterInfoParam.swift */; };
		7EA89295295D9A4600111D0D /* CIPromotionVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EA89294295D9A4600111D0D /* CIPromotionVC.swift */; };
		7EB4771F2925C44A00CF1458 /* CreatImageVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB4771E2925C44A00CF1458 /* CreatImageVC.swift */; };
		7EB477222925C87700CF1458 /* CreateImageManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477212925C87700CF1458 /* CreateImageManager.swift */; };
		7EB477242926216A00CF1458 /* CIStyleCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477232926216A00CF1458 /* CIStyleCell.swift */; };
		7EB47728292710B700CF1458 /* CTPickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47727292710B700CF1458 /* CTPickerView.swift */; };
		7EB47748292B592E00CF1458 /* IGRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47747292B592E00CF1458 /* IGRequest.swift */; };
		7EB4774B292B6ECD00CF1458 /* CIStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB4774A292B6ECD00CF1458 /* CIStyle.swift */; };
		7EB47756292B8FAC00CF1458 /* generate.pb.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB4774D292B8FAB00CF1458 /* generate.pb.swift */; };
		7EB47757292B8FAC00CF1458 /* feeds.pb.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB4774E292B8FAB00CF1458 /* feeds.pb.swift */; };
		7EB47758292B8FAC00CF1458 /* config.pb.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB4774F292B8FAB00CF1458 /* config.pb.swift */; };
		7EB47759292B8FAC00CF1458 /* prompt.pb.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47750292B8FAB00CF1458 /* prompt.pb.swift */; };
		7EB4775A292B8FAC00CF1458 /* ml.pb.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47751292B8FAB00CF1458 /* ml.pb.swift */; };
		7EB4775B292B8FAC00CF1458 /* job.pb.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47752292B8FAB00CF1458 /* job.pb.swift */; };
		7EB4775C292B8FAC00CF1458 /* user.pb.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47753292B8FAB00CF1458 /* user.pb.swift */; };
		7EB4775D292B8FAC00CF1458 /* model.pb.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47754292B8FAB00CF1458 /* model.pb.swift */; };
		7EB4775E292B8FAC00CF1458 /* character.pb.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47755292B8FAB00CF1458 /* character.pb.swift */; };
		7EB47761292B9B2900CF1458 /* CIGeneratingParam.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47760292B9B2800CF1458 /* CIGeneratingParam.swift */; };
		7EB47763292E0AB400CF1458 /* CIGeneratingModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47762292E0AB400CF1458 /* CIGeneratingModel.swift */; };
		7EB47765292E1CAF00CF1458 /* IGBaseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47764292E1CAF00CF1458 /* IGBaseModel.swift */; };
		7EB4776B292E56CA00CF1458 /* CIStatusView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47767292E56CA00CF1458 /* CIStatusView.swift */; };
		7EB4776C292E56CA00CF1458 /* CIPageControl.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47768292E56CA00CF1458 /* CIPageControl.swift */; };
		7EB4776D292E56CA00CF1458 /* CIScrollImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47769292E56CA00CF1458 /* CIScrollImageView.swift */; };
		7EB4776E292E56CA00CF1458 /* CIScrollImageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB4776A292E56CA00CF1458 /* CIScrollImageCell.swift */; };
		7EB47773292E570D00CF1458 /* CWBanner.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47770292E570D00CF1458 /* CWBanner.swift */; };
		7EB47774292E570D00CF1458 /* CWSwiftFlowLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47771292E570D00CF1458 /* CWSwiftFlowLayout.swift */; };
		7EB47775292E570D00CF1458 /* CWProxy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47772292E570D00CF1458 /* CWProxy.swift */; };
		7EB47777292E573400CF1458 /* ImageCreateDetailVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47776292E573400CF1458 /* ImageCreateDetailVC.swift */; };
		7EB47779292E578F00CF1458 /* ImageDetailVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB47778292E578F00CF1458 /* ImageDetailVC.swift */; };
		7EB477A829309A8200CF1458 /* HomeFeedCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477A729309A8200CF1458 /* HomeFeedCell.swift */; };
		7EB477AA29309BCE00CF1458 /* HomeFeedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477A929309BCE00CF1458 /* HomeFeedView.swift */; };
		7EB477AC2930F6AF00CF1458 /* UIButton_expand.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477AB2930F6AF00CF1458 /* UIButton_expand.swift */; };
		7EB477AE29310C8E00CF1458 /* FriendsProfileVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477AD29310C8E00CF1458 /* FriendsProfileVC.swift */; };
		7EB477B129324BE300CF1458 /* HomeManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477B029324BE300CF1458 /* HomeManager.swift */; };
		7EB477B329324E2000CF1458 /* MenuHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477B229324E2000CF1458 /* MenuHeaderView.swift */; };
		7EB477D12937611A00CF1458 /* LXMHeaderFooterFlowLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477C92937611A00CF1458 /* LXMHeaderFooterFlowLayout.swift */; };
		7EB477D22937611A00CF1458 /* LXMLayoutHeaderFooterProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477CA2937611A00CF1458 /* LXMLayoutHeaderFooterProtocol.swift */; };
		7EB477D32937611A00CF1458 /* CIProfileFriendView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477CB2937611A00CF1458 /* CIProfileFriendView.swift */; };
		7EB477D42937611A00CF1458 /* CIProfileListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477CC2937611A00CF1458 /* CIProfileListCell.swift */; };
		7EB477D52937611A00CF1458 /* CIProfileControl.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477CD2937611A00CF1458 /* CIProfileControl.swift */; };
		7EB477D62937611A00CF1458 /* CIProfileHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477CE2937611A00CF1458 /* CIProfileHeaderView.swift */; };
		7EB477D72937611A00CF1458 /* CIProfileView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477CF2937611A00CF1458 /* CIProfileView.swift */; };
		7EB477D82937611A00CF1458 /* CIProfileUserView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477D02937611A00CF1458 /* CIProfileUserView.swift */; };
		7EB477DA29387DF900CF1458 /* IGAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477D929387DF900CF1458 /* IGAPI.swift */; };
		7EB477DC2938B24B00CF1458 /* HomeFeedModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477DB2938B24B00CF1458 /* HomeFeedModel.swift */; };
		7EB477E1293A10BA00CF1458 /* ProfileManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477E0293A10BA00CF1458 /* ProfileManager.swift */; };
		7EB477E3293A10F600CF1458 /* UserInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477E2293A10F600CF1458 /* UserInfoModel.swift */; };
		7EB477E5293A50BB00CF1458 /* ProfileResultModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477E4293A50BB00CF1458 /* ProfileResultModel.swift */; };
		7EB477E7293A511F00CF1458 /* ImageInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477E6293A511F00CF1458 /* ImageInfoModel.swift */; };
		7EB477E9293A53A800CF1458 /* ImageListParam.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477E8293A53A800CF1458 /* ImageListParam.swift */; };
		7EB477EB293E1C4B00CF1458 /* UINavigationController_HideNav.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477EA293E1C4B00CF1458 /* UINavigationController_HideNav.swift */; };
		7EB477EF293EE5DF00CF1458 /* AmpConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477ED293EE5DF00CF1458 /* AmpConstants.swift */; };
		7EB477F0293EE5DF00CF1458 /* AmpManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EB477EE293EE5DF00CF1458 /* AmpManager.swift */; };
		7EBDBF43291B88A1008D9AFB /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EBDBF42291B88A1008D9AFB /* AppDelegate.swift */; };
		7EBDBF4C291B88A2008D9AFB /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 7EBDBF4B291B88A2008D9AFB /* Assets.xcassets */; };
		7EBDBF4F291B88A2008D9AFB /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 7EBDBF4D291B88A2008D9AFB /* LaunchScreen.storyboard */; };
		7EBDBF68291CADB7008D9AFB /* HomeVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EBDBF67291CADB7008D9AFB /* HomeVC.swift */; };
		7EBDBF6E291CCF56008D9AFB /* LoginVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EBDBF6D291CCF56008D9AFB /* LoginVC.swift */; };
		7EBDBF70291CCF62008D9AFB /* MenuVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EBDBF6F291CCF62008D9AFB /* MenuVC.swift */; };
		7EBDBF72291CCF7A008D9AFB /* ProfileVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EBDBF71291CCF7A008D9AFB /* ProfileVC.swift */; };
		7EBDBF77291CD00B008D9AFB /* InboxVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EBDBF76291CD00B008D9AFB /* InboxVC.swift */; };
		7EBE2F4F298501B50021CD84 /* FriendVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EBE2F4E298501B50021CD84 /* FriendVC.swift */; };
		7EBE2F51298504AC0021CD84 /* ContactsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EBE2F50298504AC0021CD84 /* ContactsVC.swift */; };
		7EBE2F54298505530021CD84 /* ContactsManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EBE2F53298505530021CD84 /* ContactsManager.swift */; };
		7EBE2F5629866CB10021CD84 /* StyleListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7EBE2F5529866CB10021CD84 /* StyleListVC.swift */; };
		8E235F4129A5AEEC00942493 /* character_loading.gif in Resources */ = {isa = PBXBuildFile; fileRef = 8E235F4029A5AEEB00942493 /* character_loading.gif */; };
		8E235F4329A5B49600942493 /* TimerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E235F4229A5B49600942493 /* TimerView.swift */; };
		8E267E8F295B97DE00CC50BE /* APNSModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267E8E295B97DE00CC50BE /* APNSModel.swift */; };
		8E267E91295C2A7A00CC50BE /* JumpToDiscordView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267E90295C2A7A00CC50BE /* JumpToDiscordView.swift */; };
		8E267E93295E547C00CC50BE /* EmailLoginVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267E92295E547C00CC50BE /* EmailLoginVC.swift */; };
		8E267E96295E976E00CC50BE /* PasswordHash.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267E95295E976E00CC50BE /* PasswordHash.swift */; };
		8E267E98295E9BEE00CC50BE /* EmailRegisterVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267E97295E9BEE00CC50BE /* EmailRegisterVC.swift */; };
		8E267E9A2964B8B300CC50BE /* SwipeNavigationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267E992964B8B300CC50BE /* SwipeNavigationController.swift */; };
		8E267E9C2964C81F00CC50BE /* ForgetPasswordView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267E9B2964C81F00CC50BE /* ForgetPasswordView.swift */; };
		8E267E9E2964C84000CC50BE /* EnterPinView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267E9D2964C84000CC50BE /* EnterPinView.swift */; };
		8E267EA02964E78E00CC50BE /* ForgetPasswordViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267E9F2964E78E00CC50BE /* ForgetPasswordViewModel.swift */; };
		8E267EA229665BF800CC50BE /* ResetPasswordView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267EA129665BF800CC50BE /* ResetPasswordView.swift */; };
		8E267EA429667A7C00CC50BE /* ForgetPasswordVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267EA329667A7C00CC50BE /* ForgetPasswordVC.swift */; };
		8E267EA62967B74A00CC50BE /* EnterPinVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267EA52967B74A00CC50BE /* EnterPinVC.swift */; };
		8E267EA92968AD7D00CC50BE /* ResetPasswordModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267EA82968AD7D00CC50BE /* ResetPasswordModel.swift */; };
		8E267EAB296BDC0B00CC50BE /* ResetPasswordVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267EAA296BDC0B00CC50BE /* ResetPasswordVC.swift */; };
		8E267EAD296D257700CC50BE /* OTPViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267EAC296D257700CC50BE /* OTPViewModel.swift */; };
		8E267EAF296D25D700CC50BE /* ResetPasswordViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267EAE296D25D700CC50BE /* ResetPasswordViewModel.swift */; };
		8E267EB1296DF63E00CC50BE /* Validation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E267EB0296DF63E00CC50BE /* Validation.swift */; };
		8E692E01292E126F007D50BF /* ImageDetailModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E692E00292E126F007D50BF /* ImageDetailModel.swift */; };
		8E692E04292E12B5007D50BF /* ImageDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E692E03292E12B5007D50BF /* ImageDetailView.swift */; };
		8E692E06292E1342007D50BF /* FullImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E692E05292E1342007D50BF /* FullImageView.swift */; };
		8E692E16293481EF007D50BF /* ImageDetailViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E692E15293481EF007D50BF /* ImageDetailViewModel.swift */; };
		8E692E192937EEF8007D50BF /* ColorTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E692E182937EEF8007D50BF /* ColorTransform.swift */; };
		8E6E5BA1299C6B3300B099F9 /* CommentModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E6E5BA0299C6B3300B099F9 /* CommentModel.swift */; };
		8E6E5BA329A4261800B099F9 /* TutorialView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E6E5BA229A4261800B099F9 /* TutorialView.swift */; };
		8E6E5BA529A44F4700B099F9 /* NewPhotoPickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E6E5BA429A44F4700B099F9 /* NewPhotoPickerView.swift */; };
		8E6E5BA729A460F100B099F9 /* InputNameView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E6E5BA629A460F100B099F9 /* InputNameView.swift */; };
		8E6E5BA929A463E900B099F9 /* InputNameViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E6E5BA829A463E900B099F9 /* InputNameViewModel.swift */; };
		8E6E5BAB29A58B7100B099F9 /* WaitingOutputView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E6E5BAA29A58B7100B099F9 /* WaitingOutputView.swift */; };
		8EB8864429B706E1006FC905 /* DraftView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8EB8864329B706E1006FC905 /* DraftView.swift */; };
		8EF824E32942DA6500C7E9FE /* AddPhotoButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8EF824E22942DA6500C7E9FE /* AddPhotoButton.swift */; };
		8EF824E7294AD62600C7E9FE /* Poppins-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8EF824E6294AD62600C7E9FE /* Poppins-Regular.ttf */; };
		8EF824E9294AD85E00C7E9FE /* Poppins-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8EF824E8294AD85E00C7E9FE /* Poppins-SemiBold.ttf */; };
		8EF824F12952B5FD00C7E9FE /* LoginView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8EF824F02952B5FD00C7E9FE /* LoginView.swift */; };
		8EF824F3295396F900C7E9FE /* ImageModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8EF824F2295396F900C7E9FE /* ImageModifier.swift */; };
		8EF824F52953CFC300C7E9FE /* SignUpViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8EF824F42953CFC300C7E9FE /* SignUpViewModel.swift */; };
		8EF824F72953E98800C7E9FE /* SignUpView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8EF824F62953E98800C7E9FE /* SignUpView.swift */; };
		8EF824F92954E93800C7E9FE /* PasswordView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8EF824F82954E93800C7E9FE /* PasswordView.swift */; };
		8EF824FD29552ED000C7E9FE /* LogInViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8EF824FC29552ED000C7E9FE /* LogInViewModel.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0EAAAB5F046B77EF8DD4E55D /* Pods-Imagine.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Imagine.debug.xcconfig"; path = "Target Support Files/Pods-Imagine/Pods-Imagine.debug.xcconfig"; sourceTree = "<group>"; };
		1A0F6B13338801DBFF6049ED /* Pods-Imagine.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Imagine.release.xcconfig"; path = "Target Support Files/Pods-Imagine/Pods-Imagine.release.xcconfig"; sourceTree = "<group>"; };
		4694715416F55B67DBD555F3 /* Pods-aiplayground.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-aiplayground.debug.xcconfig"; path = "Target Support Files/Pods-aiplayground/Pods-aiplayground.debug.xcconfig"; sourceTree = "<group>"; };
		5C37A6206362D7B8BB99C45E /* Pods-aiplayground.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-aiplayground.release.xcconfig"; path = "Target Support Files/Pods-aiplayground/Pods-aiplayground.release.xcconfig"; sourceTree = "<group>"; };
		7E3453FA2921F395001276CF /* PGTabbarController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PGTabbarController.swift; sourceTree = "<group>"; };
		7E34541F292240DF001276CF /* LoginManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginManager.swift; sourceTree = "<group>"; };
		7E345421292323C9001276CF /* KeyChainManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeyChainManager.swift; sourceTree = "<group>"; };
		7E34542429239FB8001276CF /* CIPromptView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CIPromptView.swift; sourceTree = "<group>"; };
		7E34542629239FD2001276CF /* CIStyleView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CIStyleView.swift; sourceTree = "<group>"; };
		7E34542829239FF1001276CF /* CINumberView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CINumberView.swift; sourceTree = "<group>"; };
		7E34542A2923A028001276CF /* CITitleView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CITitleView.swift; sourceTree = "<group>"; };
		7E34542D2924B7E0001276CF /* UIDefine.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIDefine.swift; sourceTree = "<group>"; };
		7E34542F2924DD2C001276CF /* UIColor_extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIColor_extension.swift; sourceTree = "<group>"; };
		7E348C3B2941BA2B005BA9BE /* PhotoPickerViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PhotoPickerViewModel.swift; sourceTree = "<group>"; };
		7E348C3C2941BA2B005BA9BE /* PhotoPicker.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PhotoPicker.swift; sourceTree = "<group>"; };
		7E348C3E2941BA2B005BA9BE /* PhotoPickerView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PhotoPickerView.swift; sourceTree = "<group>"; };
		7E348C3F2941BA2B005BA9BE /* PhotoCellView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PhotoCellView.swift; sourceTree = "<group>"; };
		7E348C442941BB5A005BA9BE /* CICharacterVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CICharacterVC.swift; sourceTree = "<group>"; };
		7E348C462941D81E005BA9BE /* CICharacterListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CICharacterListView.swift; sourceTree = "<group>"; };
		7E348C482941DC9E005BA9BE /* SwiftUIColor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwiftUIColor.swift; sourceTree = "<group>"; };
		7E348C4C2941E1B6005BA9BE /* CICharacterListCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CICharacterListCell.swift; sourceTree = "<group>"; };
		7E348C54294707DD005BA9BE /* CICharacterModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CICharacterModel.swift; sourceTree = "<group>"; };
		7E5536D2299117C7001CAD68 /* ContactsGuideVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContactsGuideVC.swift; sourceTree = "<group>"; };
		7E5536D42992322B001CAD68 /* ContactsCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContactsCell.swift; sourceTree = "<group>"; };
		7E5536D6299232A4001CAD68 /* Contact.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Contact.swift; sourceTree = "<group>"; };
		7E5536E229927169001CAD68 /* ContactModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContactModel.swift; sourceTree = "<group>"; };
		7E5536E5299273E8001CAD68 /* PersistentContainer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PersistentContainer.swift; sourceTree = "<group>"; };
		7E5536E829928C86001CAD68 /* imagine.xcdatamodel */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcdatamodel; path = imagine.xcdatamodel; sourceTree = "<group>"; };
		7E5536EA299A2E1D001CAD68 /* HomeNavigationBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeNavigationBar.swift; sourceTree = "<group>"; };
		7E5536EC299A5AF0001CAD68 /* UIImage_extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIImage_extension.swift; sourceTree = "<group>"; };
		7E5536EE299DBB2E001CAD68 /* CommentVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentVC.swift; sourceTree = "<group>"; };
		7E5536F029A318BA001CAD68 /* CommentListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentListView.swift; sourceTree = "<group>"; };
		7E5536F229A31A10001CAD68 /* CommentCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentCell.swift; sourceTree = "<group>"; };
		7E5536F429A485A4001CAD68 /* SendView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SendView.swift; sourceTree = "<group>"; };
		7E5536F629A746FA001CAD68 /* CICharacterNameVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CICharacterNameVC.swift; sourceTree = "<group>"; };
		7E5536F829A74D36001CAD68 /* CICharacterLoadingVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CICharacterLoadingVC.swift; sourceTree = "<group>"; };
		7E5536FB29AC97D0001CAD68 /* ScribbleVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScribbleVC.swift; sourceTree = "<group>"; };
		7E60D2A92965738A00D48B1A /* String_extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = String_extension.swift; sourceTree = "<group>"; };
		7E97A16C294886B800397AEA /* CIRandomPromptModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CIRandomPromptModel.swift; sourceTree = "<group>"; };
		7E97A16E2949C35600397AEA /* CICharacterNameModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CICharacterNameModel.swift; sourceTree = "<group>"; };
		7E97A1702949C70B00397AEA /* CICharacterMaterialsModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CICharacterMaterialsModel.swift; sourceTree = "<group>"; };
		7E97A178295046A800397AEA /* LoginModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginModel.swift; sourceTree = "<group>"; };
		7E97A17A29505A6500397AEA /* LoginWebVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginWebVC.swift; sourceTree = "<group>"; };
		7EA892702951A84E00111D0D /* UIViewController_extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIViewController_extension.swift; sourceTree = "<group>"; };
		7EA892722951E1F800111D0D /* IGNavigationViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IGNavigationViewController.swift; sourceTree = "<group>"; };
		7EA892742951E2B400111D0D /* IGBaseViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IGBaseViewController.swift; sourceTree = "<group>"; };
		7EA89277295314F500111D0D /* FaceCropper.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FaceCropper.swift; sourceTree = "<group>"; };
		7EA89279295C0BA700111D0D /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		7EA8927C295C2F0100111D0D /* CharacterVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CharacterVC.swift; sourceTree = "<group>"; };
		7EA8927E295C30B200111D0D /* CharacterImagesVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CharacterImagesVC.swift; sourceTree = "<group>"; };
		7EA89281295C32EB00111D0D /* CharacterCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CharacterCell.swift; sourceTree = "<group>"; };
		7EA89283295C32FB00111D0D /* CharacterImagesCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CharacterImagesCell.swift; sourceTree = "<group>"; };
		7EA89285295C33A600111D0D /* CharacterListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CharacterListView.swift; sourceTree = "<group>"; };
		7EA89287295C33D600111D0D /* CharacterImagesListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CharacterImagesListView.swift; sourceTree = "<group>"; };
		7EA8928A295D835300111D0D /* CharacterManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CharacterManager.swift; sourceTree = "<group>"; };
		7EA8928C295D83A800111D0D /* CharacterListModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CharacterListModel.swift; sourceTree = "<group>"; };
		7EA8928E295D83B400111D0D /* CharacterInfoModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CharacterInfoModel.swift; sourceTree = "<group>"; };
		7EA89290295D84F100111D0D /* CharacterListParam.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CharacterListParam.swift; sourceTree = "<group>"; };
		7EA89292295D85B600111D0D /* CharacterInfoParam.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CharacterInfoParam.swift; sourceTree = "<group>"; };
		7EA89294295D9A4600111D0D /* CIPromotionVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CIPromotionVC.swift; sourceTree = "<group>"; };
		7EB4771E2925C44A00CF1458 /* CreatImageVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreatImageVC.swift; sourceTree = "<group>"; };
		7EB477212925C87700CF1458 /* CreateImageManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreateImageManager.swift; sourceTree = "<group>"; };
		7EB477232926216A00CF1458 /* CIStyleCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CIStyleCell.swift; sourceTree = "<group>"; };
		7EB47727292710B700CF1458 /* CTPickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CTPickerView.swift; sourceTree = "<group>"; };
		7EB47747292B592E00CF1458 /* IGRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IGRequest.swift; sourceTree = "<group>"; };
		7EB4774A292B6ECD00CF1458 /* CIStyle.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CIStyle.swift; sourceTree = "<group>"; };
		7EB4774D292B8FAB00CF1458 /* generate.pb.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = generate.pb.swift; sourceTree = "<group>"; };
		7EB4774E292B8FAB00CF1458 /* feeds.pb.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = feeds.pb.swift; sourceTree = "<group>"; };
		7EB4774F292B8FAB00CF1458 /* config.pb.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = config.pb.swift; sourceTree = "<group>"; };
		7EB47750292B8FAB00CF1458 /* prompt.pb.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = prompt.pb.swift; sourceTree = "<group>"; };
		7EB47751292B8FAB00CF1458 /* ml.pb.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ml.pb.swift; sourceTree = "<group>"; };
		7EB47752292B8FAB00CF1458 /* job.pb.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = job.pb.swift; sourceTree = "<group>"; };
		7EB47753292B8FAB00CF1458 /* user.pb.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = user.pb.swift; sourceTree = "<group>"; };
		7EB47754292B8FAB00CF1458 /* model.pb.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = model.pb.swift; sourceTree = "<group>"; };
		7EB47755292B8FAB00CF1458 /* character.pb.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = character.pb.swift; sourceTree = "<group>"; };
		7EB4775F292B923E00CF1458 /* create.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = create.md; sourceTree = "<group>"; };
		7EB47760292B9B2800CF1458 /* CIGeneratingParam.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CIGeneratingParam.swift; sourceTree = "<group>"; };
		7EB47762292E0AB400CF1458 /* CIGeneratingModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CIGeneratingModel.swift; sourceTree = "<group>"; };
		7EB47764292E1CAF00CF1458 /* IGBaseModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IGBaseModel.swift; sourceTree = "<group>"; };
		7EB47767292E56CA00CF1458 /* CIStatusView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CIStatusView.swift; sourceTree = "<group>"; };
		7EB47768292E56CA00CF1458 /* CIPageControl.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CIPageControl.swift; sourceTree = "<group>"; };
		7EB47769292E56CA00CF1458 /* CIScrollImageView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CIScrollImageView.swift; sourceTree = "<group>"; };
		7EB4776A292E56CA00CF1458 /* CIScrollImageCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CIScrollImageCell.swift; sourceTree = "<group>"; };
		7EB47770292E570D00CF1458 /* CWBanner.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CWBanner.swift; sourceTree = "<group>"; };
		7EB47771292E570D00CF1458 /* CWSwiftFlowLayout.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CWSwiftFlowLayout.swift; sourceTree = "<group>"; };
		7EB47772292E570D00CF1458 /* CWProxy.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CWProxy.swift; sourceTree = "<group>"; };
		7EB47776292E573400CF1458 /* ImageCreateDetailVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ImageCreateDetailVC.swift; sourceTree = "<group>"; };
		7EB47778292E578F00CF1458 /* ImageDetailVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageDetailVC.swift; sourceTree = "<group>"; };
		7EB477A729309A8200CF1458 /* HomeFeedCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeFeedCell.swift; sourceTree = "<group>"; };
		7EB477A929309BCE00CF1458 /* HomeFeedView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeFeedView.swift; sourceTree = "<group>"; };
		7EB477AB2930F6AF00CF1458 /* UIButton_expand.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIButton_expand.swift; sourceTree = "<group>"; };
		7EB477AD29310C8E00CF1458 /* FriendsProfileVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FriendsProfileVC.swift; sourceTree = "<group>"; };
		7EB477B029324BE300CF1458 /* HomeManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeManager.swift; sourceTree = "<group>"; };
		7EB477B229324E2000CF1458 /* MenuHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MenuHeaderView.swift; sourceTree = "<group>"; };
		7EB477C92937611A00CF1458 /* LXMHeaderFooterFlowLayout.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LXMHeaderFooterFlowLayout.swift; sourceTree = "<group>"; };
		7EB477CA2937611A00CF1458 /* LXMLayoutHeaderFooterProtocol.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LXMLayoutHeaderFooterProtocol.swift; sourceTree = "<group>"; };
		7EB477CB2937611A00CF1458 /* CIProfileFriendView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CIProfileFriendView.swift; sourceTree = "<group>"; };
		7EB477CC2937611A00CF1458 /* CIProfileListCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CIProfileListCell.swift; sourceTree = "<group>"; };
		7EB477CD2937611A00CF1458 /* CIProfileControl.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CIProfileControl.swift; sourceTree = "<group>"; };
		7EB477CE2937611A00CF1458 /* CIProfileHeaderView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CIProfileHeaderView.swift; sourceTree = "<group>"; };
		7EB477CF2937611A00CF1458 /* CIProfileView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CIProfileView.swift; sourceTree = "<group>"; };
		7EB477D02937611A00CF1458 /* CIProfileUserView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CIProfileUserView.swift; sourceTree = "<group>"; };
		7EB477D929387DF900CF1458 /* IGAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IGAPI.swift; sourceTree = "<group>"; };
		7EB477DB2938B24B00CF1458 /* HomeFeedModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeFeedModel.swift; sourceTree = "<group>"; };
		7EB477E0293A10BA00CF1458 /* ProfileManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileManager.swift; sourceTree = "<group>"; };
		7EB477E2293A10F600CF1458 /* UserInfoModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserInfoModel.swift; sourceTree = "<group>"; };
		7EB477E4293A50BB00CF1458 /* ProfileResultModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileResultModel.swift; sourceTree = "<group>"; };
		7EB477E6293A511F00CF1458 /* ImageInfoModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageInfoModel.swift; sourceTree = "<group>"; };
		7EB477E8293A53A800CF1458 /* ImageListParam.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageListParam.swift; sourceTree = "<group>"; };
		7EB477EA293E1C4B00CF1458 /* UINavigationController_HideNav.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UINavigationController_HideNav.swift; sourceTree = "<group>"; };
		7EB477ED293EE5DF00CF1458 /* AmpConstants.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AmpConstants.swift; sourceTree = "<group>"; };
		7EB477EE293EE5DF00CF1458 /* AmpManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AmpManager.swift; sourceTree = "<group>"; };
		7EBDBF3F291B88A1008D9AFB /* Imagine.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Imagine.app; sourceTree = BUILT_PRODUCTS_DIR; };
		7EBDBF42291B88A1008D9AFB /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		7EBDBF4B291B88A2008D9AFB /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		7EBDBF4E291B88A2008D9AFB /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		7EBDBF50291B88A2008D9AFB /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		7EBDBF67291CADB7008D9AFB /* HomeVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeVC.swift; sourceTree = "<group>"; };
		7EBDBF6D291CCF56008D9AFB /* LoginVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginVC.swift; sourceTree = "<group>"; };
		7EBDBF6F291CCF62008D9AFB /* MenuVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MenuVC.swift; sourceTree = "<group>"; };
		7EBDBF71291CCF7A008D9AFB /* ProfileVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileVC.swift; sourceTree = "<group>"; };
		7EBDBF76291CD00B008D9AFB /* InboxVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InboxVC.swift; sourceTree = "<group>"; };
		7EBE2F4E298501B50021CD84 /* FriendVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FriendVC.swift; sourceTree = "<group>"; };
		7EBE2F50298504AC0021CD84 /* ContactsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContactsVC.swift; sourceTree = "<group>"; };
		7EBE2F53298505530021CD84 /* ContactsManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContactsManager.swift; sourceTree = "<group>"; };
		7EBE2F5529866CB10021CD84 /* StyleListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StyleListVC.swift; sourceTree = "<group>"; };
		8D129A29723C0FF96CB93692 /* Pods_Imagine.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Imagine.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		8E235F4029A5AEEB00942493 /* character_loading.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = character_loading.gif; sourceTree = "<group>"; };
		8E235F4229A5B49600942493 /* TimerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TimerView.swift; sourceTree = "<group>"; };
		8E267E8E295B97DE00CC50BE /* APNSModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APNSModel.swift; sourceTree = "<group>"; };
		8E267E90295C2A7A00CC50BE /* JumpToDiscordView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JumpToDiscordView.swift; sourceTree = "<group>"; };
		8E267E92295E547C00CC50BE /* EmailLoginVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmailLoginVC.swift; sourceTree = "<group>"; };
		8E267E95295E976E00CC50BE /* PasswordHash.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PasswordHash.swift; sourceTree = "<group>"; };
		8E267E97295E9BEE00CC50BE /* EmailRegisterVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmailRegisterVC.swift; sourceTree = "<group>"; };
		8E267E992964B8B300CC50BE /* SwipeNavigationController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwipeNavigationController.swift; sourceTree = "<group>"; };
		8E267E9B2964C81F00CC50BE /* ForgetPasswordView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ForgetPasswordView.swift; sourceTree = "<group>"; };
		8E267E9D2964C84000CC50BE /* EnterPinView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnterPinView.swift; sourceTree = "<group>"; };
		8E267E9F2964E78E00CC50BE /* ForgetPasswordViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ForgetPasswordViewModel.swift; sourceTree = "<group>"; };
		8E267EA129665BF800CC50BE /* ResetPasswordView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResetPasswordView.swift; sourceTree = "<group>"; };
		8E267EA329667A7C00CC50BE /* ForgetPasswordVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ForgetPasswordVC.swift; sourceTree = "<group>"; };
		8E267EA52967B74A00CC50BE /* EnterPinVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnterPinVC.swift; sourceTree = "<group>"; };
		8E267EA82968AD7D00CC50BE /* ResetPasswordModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResetPasswordModel.swift; sourceTree = "<group>"; };
		8E267EAA296BDC0B00CC50BE /* ResetPasswordVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResetPasswordVC.swift; sourceTree = "<group>"; };
		8E267EAC296D257700CC50BE /* OTPViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OTPViewModel.swift; sourceTree = "<group>"; };
		8E267EAE296D25D700CC50BE /* ResetPasswordViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResetPasswordViewModel.swift; sourceTree = "<group>"; };
		8E267EB0296DF63E00CC50BE /* Validation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Validation.swift; sourceTree = "<group>"; };
		8E692E00292E126F007D50BF /* ImageDetailModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageDetailModel.swift; sourceTree = "<group>"; };
		8E692E03292E12B5007D50BF /* ImageDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageDetailView.swift; sourceTree = "<group>"; };
		8E692E05292E1342007D50BF /* FullImageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FullImageView.swift; sourceTree = "<group>"; };
		8E692E15293481EF007D50BF /* ImageDetailViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageDetailViewModel.swift; sourceTree = "<group>"; };
		8E692E182937EEF8007D50BF /* ColorTransform.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ColorTransform.swift; sourceTree = "<group>"; };
		8E6E5BA0299C6B3300B099F9 /* CommentModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentModel.swift; sourceTree = "<group>"; };
		8E6E5BA229A4261800B099F9 /* TutorialView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TutorialView.swift; sourceTree = "<group>"; };
		8E6E5BA429A44F4700B099F9 /* NewPhotoPickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewPhotoPickerView.swift; sourceTree = "<group>"; };
		8E6E5BA629A460F100B099F9 /* InputNameView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InputNameView.swift; sourceTree = "<group>"; };
		8E6E5BA829A463E900B099F9 /* InputNameViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InputNameViewModel.swift; sourceTree = "<group>"; };
		8E6E5BAA29A58B7100B099F9 /* WaitingOutputView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WaitingOutputView.swift; sourceTree = "<group>"; };
		8EB8864329B706E1006FC905 /* DraftView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DraftView.swift; sourceTree = "<group>"; };
		8EF824E22942DA6500C7E9FE /* AddPhotoButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddPhotoButton.swift; sourceTree = "<group>"; };
		8EF824E6294AD62600C7E9FE /* Poppins-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-Regular.ttf"; sourceTree = "<group>"; };
		8EF824E8294AD85E00C7E9FE /* Poppins-SemiBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-SemiBold.ttf"; sourceTree = "<group>"; };
		8EF824F02952B5FD00C7E9FE /* LoginView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginView.swift; sourceTree = "<group>"; };
		8EF824F2295396F900C7E9FE /* ImageModifier.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageModifier.swift; sourceTree = "<group>"; };
		8EF824F42953CFC300C7E9FE /* SignUpViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SignUpViewModel.swift; sourceTree = "<group>"; };
		8EF824F62953E98800C7E9FE /* SignUpView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SignUpView.swift; sourceTree = "<group>"; };
		8EF824F82954E93800C7E9FE /* PasswordView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PasswordView.swift; sourceTree = "<group>"; };
		8EF824FC29552ED000C7E9FE /* LogInViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LogInViewModel.swift; sourceTree = "<group>"; };
		8EF824FE295A420000C7E9FE /* aiplayground.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = aiplayground.entitlements; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7EBDBF3C291B88A1008D9AFB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0909D9D3F3E8A4E1A0334C6D /* Pods_Imagine.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0724DA672930A98F00899A6B /* view */ = {
			isa = PBXGroup;
			children = (
				7EB477CD2937611A00CF1458 /* CIProfileControl.swift */,
				7EB477CE2937611A00CF1458 /* CIProfileHeaderView.swift */,
				7EB477CC2937611A00CF1458 /* CIProfileListCell.swift */,
				7EB477D02937611A00CF1458 /* CIProfileUserView.swift */,
				7EB477CB2937611A00CF1458 /* CIProfileFriendView.swift */,
				7EB477CF2937611A00CF1458 /* CIProfileView.swift */,
				7EB477C82937611A00CF1458 /* layout */,
			);
			path = view;
			sourceTree = "<group>";
		};
		5CCE3798C9DA015A4CA808AB /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				8D129A29723C0FF96CB93692 /* Pods_Imagine.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		7E34541C29223FDB001276CF /* login */ = {
			isa = PBXGroup;
			children = (
				7E34541F292240DF001276CF /* LoginManager.swift */,
				7E345421292323C9001276CF /* KeyChainManager.swift */,
				7E97A178295046A800397AEA /* LoginModel.swift */,
			);
			path = login;
			sourceTree = "<group>";
		};
		7E34542329239F8C001276CF /* view */ = {
			isa = PBXGroup;
			children = (
				7E34542429239FB8001276CF /* CIPromptView.swift */,
				7E34542629239FD2001276CF /* CIStyleView.swift */,
				7EB477232926216A00CF1458 /* CIStyleCell.swift */,
				7E34542829239FF1001276CF /* CINumberView.swift */,
				7E34542A2923A028001276CF /* CITitleView.swift */,
				7EB47727292710B700CF1458 /* CTPickerView.swift */,
				7E348C462941D81E005BA9BE /* CICharacterListView.swift */,
				7E348C4C2941E1B6005BA9BE /* CICharacterListCell.swift */,
				7EB47766292E56CA00CF1458 /* detail */,
			);
			path = view;
			sourceTree = "<group>";
		};
		7E34542C2924B7A4001276CF /* Tool */ = {
			isa = PBXGroup;
			children = (
				7EA89276295314F500111D0D /* face */,
				7E34542D2924B7E0001276CF /* UIDefine.swift */,
				7E34542F2924DD2C001276CF /* UIColor_extension.swift */,
				7E5536EC299A5AF0001CAD68 /* UIImage_extension.swift */,
				7EB477AB2930F6AF00CF1458 /* UIButton_expand.swift */,
				7E348C482941DC9E005BA9BE /* SwiftUIColor.swift */,
				7EA892702951A84E00111D0D /* UIViewController_extension.swift */,
				7E60D2A92965738A00D48B1A /* String_extension.swift */,
			);
			path = Tool;
			sourceTree = "<group>";
		};
		7E348C392941BA2B005BA9BE /* PhotoPicker */ = {
			isa = PBXGroup;
			children = (
				7E348C3A2941BA2B005BA9BE /* viewmodel */,
				7E348C3D2941BA2B005BA9BE /* view */,
			);
			path = PhotoPicker;
			sourceTree = "<group>";
		};
		7E348C3A2941BA2B005BA9BE /* viewmodel */ = {
			isa = PBXGroup;
			children = (
				7E348C3B2941BA2B005BA9BE /* PhotoPickerViewModel.swift */,
				7E348C3C2941BA2B005BA9BE /* PhotoPicker.swift */,
				8E6E5BA829A463E900B099F9 /* InputNameViewModel.swift */,
			);
			path = viewmodel;
			sourceTree = "<group>";
		};
		7E348C3D2941BA2B005BA9BE /* view */ = {
			isa = PBXGroup;
			children = (
				7E348C3E2941BA2B005BA9BE /* PhotoPickerView.swift */,
				7E348C3F2941BA2B005BA9BE /* PhotoCellView.swift */,
				8EF824E22942DA6500C7E9FE /* AddPhotoButton.swift */,
				8E6E5BA229A4261800B099F9 /* TutorialView.swift */,
				8E6E5BA429A44F4700B099F9 /* NewPhotoPickerView.swift */,
				8E6E5BA629A460F100B099F9 /* InputNameView.swift */,
				8E6E5BAA29A58B7100B099F9 /* WaitingOutputView.swift */,
				8E235F4229A5B49600942493 /* TimerView.swift */,
			);
			path = view;
			sourceTree = "<group>";
		};
		7E5536E4299273DF001CAD68 /* coredata */ = {
			isa = PBXGroup;
			children = (
				7E5536E5299273E8001CAD68 /* PersistentContainer.swift */,
			);
			path = coredata;
			sourceTree = "<group>";
		};
		7E5536FA29AC97A0001CAD68 /* Scribble */ = {
			isa = PBXGroup;
			children = (
				7E5536FB29AC97D0001CAD68 /* ScribbleVC.swift */,
			);
			path = Scribble;
			sourceTree = "<group>";
		};
		7EA89276295314F500111D0D /* face */ = {
			isa = PBXGroup;
			children = (
				7EA89277295314F500111D0D /* FaceCropper.swift */,
			);
			path = face;
			sourceTree = "<group>";
		};
		7EA8927B295C2EE900111D0D /* Character */ = {
			isa = PBXGroup;
			children = (
				7EA8927C295C2F0100111D0D /* CharacterVC.swift */,
				7EA8927E295C30B200111D0D /* CharacterImagesVC.swift */,
				7EA89280295C30E000111D0D /* view */,
			);
			path = Character;
			sourceTree = "<group>";
		};
		7EA89280295C30E000111D0D /* view */ = {
			isa = PBXGroup;
			children = (
				7EA89285295C33A600111D0D /* CharacterListView.swift */,
				7EA89281295C32EB00111D0D /* CharacterCell.swift */,
				7EA89287295C33D600111D0D /* CharacterImagesListView.swift */,
				7EA89283295C32FB00111D0D /* CharacterImagesCell.swift */,
			);
			path = view;
			sourceTree = "<group>";
		};
		7EA89289295D832600111D0D /* character */ = {
			isa = PBXGroup;
			children = (
				7EA8928A295D835300111D0D /* CharacterManager.swift */,
				7EA8928C295D83A800111D0D /* CharacterListModel.swift */,
				7EA8928E295D83B400111D0D /* CharacterInfoModel.swift */,
				7EA89290295D84F100111D0D /* CharacterListParam.swift */,
				7EA89292295D85B600111D0D /* CharacterInfoParam.swift */,
			);
			path = character;
			sourceTree = "<group>";
		};
		7EB477202925C5AA00CF1458 /* create */ = {
			isa = PBXGroup;
			children = (
				7EB477212925C87700CF1458 /* CreateImageManager.swift */,
				7EB4774A292B6ECD00CF1458 /* CIStyle.swift */,
				7EB47760292B9B2800CF1458 /* CIGeneratingParam.swift */,
				7EB47762292E0AB400CF1458 /* CIGeneratingModel.swift */,
				7E348C54294707DD005BA9BE /* CICharacterModel.swift */,
				7E97A16C294886B800397AEA /* CIRandomPromptModel.swift */,
				7E97A16E2949C35600397AEA /* CICharacterNameModel.swift */,
				7E97A1702949C70B00397AEA /* CICharacterMaterialsModel.swift */,
			);
			path = create;
			sourceTree = "<group>";
		};
		7EB47729292736D400CF1458 /* view */ = {
			isa = PBXGroup;
			children = (
				8E692E03292E12B5007D50BF /* ImageDetailView.swift */,
				8E692E05292E1342007D50BF /* FullImageView.swift */,
				8EF824F2295396F900C7E9FE /* ImageModifier.swift */,
				8EB8864329B706E1006FC905 /* DraftView.swift */,
			);
			path = view;
			sourceTree = "<group>";
		};
		7EB47746292B581100CF1458 /* request */ = {
			isa = PBXGroup;
			children = (
				7EB47747292B592E00CF1458 /* IGRequest.swift */,
			);
			path = request;
			sourceTree = "<group>";
		};
		7EB4774C292B8FAB00CF1458 /* pb */ = {
			isa = PBXGroup;
			children = (
				7EB4774D292B8FAB00CF1458 /* generate.pb.swift */,
				7EB4774E292B8FAB00CF1458 /* feeds.pb.swift */,
				7EB4774F292B8FAB00CF1458 /* config.pb.swift */,
				7EB47750292B8FAB00CF1458 /* prompt.pb.swift */,
				7EB47751292B8FAB00CF1458 /* ml.pb.swift */,
				7EB47752292B8FAB00CF1458 /* job.pb.swift */,
				7EB47753292B8FAB00CF1458 /* user.pb.swift */,
				7EB47754292B8FAB00CF1458 /* model.pb.swift */,
				7EB47755292B8FAB00CF1458 /* character.pb.swift */,
				7EB4775F292B923E00CF1458 /* create.md */,
			);
			path = pb;
			sourceTree = "<group>";
		};
		7EB47766292E56CA00CF1458 /* detail */ = {
			isa = PBXGroup;
			children = (
				7EB47767292E56CA00CF1458 /* CIStatusView.swift */,
				7EB47768292E56CA00CF1458 /* CIPageControl.swift */,
				7EB47769292E56CA00CF1458 /* CIScrollImageView.swift */,
				7EB4776A292E56CA00CF1458 /* CIScrollImageCell.swift */,
				7EB4776F292E570D00CF1458 /* scrollimage */,
			);
			path = detail;
			sourceTree = "<group>";
		};
		7EB4776F292E570D00CF1458 /* scrollimage */ = {
			isa = PBXGroup;
			children = (
				7EB47770292E570D00CF1458 /* CWBanner.swift */,
				7EB47771292E570D00CF1458 /* CWSwiftFlowLayout.swift */,
				7EB47772292E570D00CF1458 /* CWProxy.swift */,
			);
			path = scrollimage;
			sourceTree = "<group>";
		};
		7EB477902930984D00CF1458 /* view */ = {
			isa = PBXGroup;
			children = (
				7EB477A929309BCE00CF1458 /* HomeFeedView.swift */,
				7EB477A729309A8200CF1458 /* HomeFeedCell.swift */,
				7E5536EA299A2E1D001CAD68 /* HomeNavigationBar.swift */,
			);
			path = view;
			sourceTree = "<group>";
		};
		7EB477AF29324BBA00CF1458 /* home */ = {
			isa = PBXGroup;
			children = (
				7EB477B029324BE300CF1458 /* HomeManager.swift */,
				7EB477DB2938B24B00CF1458 /* HomeFeedModel.swift */,
				7EB477E6293A511F00CF1458 /* ImageInfoModel.swift */,
				7EB477E8293A53A800CF1458 /* ImageListParam.swift */,
			);
			path = home;
			sourceTree = "<group>";
		};
		7EB477C82937611A00CF1458 /* layout */ = {
			isa = PBXGroup;
			children = (
				7EB477C92937611A00CF1458 /* LXMHeaderFooterFlowLayout.swift */,
				7EB477CA2937611A00CF1458 /* LXMLayoutHeaderFooterProtocol.swift */,
			);
			path = layout;
			sourceTree = "<group>";
		};
		7EB477DF293A109600CF1458 /* profile */ = {
			isa = PBXGroup;
			children = (
				7EB477E0293A10BA00CF1458 /* ProfileManager.swift */,
				7EB477E2293A10F600CF1458 /* UserInfoModel.swift */,
				7EB477E4293A50BB00CF1458 /* ProfileResultModel.swift */,
			);
			path = profile;
			sourceTree = "<group>";
		};
		7EB477EC293EE5DF00CF1458 /* Statistics */ = {
			isa = PBXGroup;
			children = (
				7EB477ED293EE5DF00CF1458 /* AmpConstants.swift */,
				7EB477EE293EE5DF00CF1458 /* AmpManager.swift */,
			);
			path = Statistics;
			sourceTree = "<group>";
		};
		7EBDBF36291B88A1008D9AFB = {
			isa = PBXGroup;
			children = (
				7EB4774C292B8FAB00CF1458 /* pb */,
				7EBDBF41291B88A1008D9AFB /* aiplayground */,
				7EBDBF40291B88A1008D9AFB /* Products */,
				E0A481F96A41C01DED0F2C36 /* Pods */,
				5CCE3798C9DA015A4CA808AB /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		7EBDBF40291B88A1008D9AFB /* Products */ = {
			isa = PBXGroup;
			children = (
				7EBDBF3F291B88A1008D9AFB /* Imagine.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7EBDBF41291B88A1008D9AFB /* aiplayground */ = {
			isa = PBXGroup;
			children = (
				8E235F3D29A5988B00942493 /* gif */,
				8EF824FE295A420000C7E9FE /* aiplayground.entitlements */,
				7EB477EC293EE5DF00CF1458 /* Statistics */,
				7EBDBF42291B88A1008D9AFB /* AppDelegate.swift */,
				7EBDBF58291CAA74008D9AFB /* ViewController */,
				7EBDBF57291CAA74008D9AFB /* Data */,
				7E34542C2924B7A4001276CF /* Tool */,
				7EBDBF4B291B88A2008D9AFB /* Assets.xcassets */,
				7EBDBF4D291B88A2008D9AFB /* LaunchScreen.storyboard */,
				7EBDBF50291B88A2008D9AFB /* Info.plist */,
				7E5536E729928C86001CAD68 /* imagine.xcdatamodeld */,
				7EA89279295C0BA700111D0D /* GoogleService-Info.plist */,
				8EF824E8294AD85E00C7E9FE /* Poppins-SemiBold.ttf */,
				8EF824E6294AD62600C7E9FE /* Poppins-Regular.ttf */,
			);
			path = aiplayground;
			sourceTree = "<group>";
		};
		7EBDBF57291CAA74008D9AFB /* Data */ = {
			isa = PBXGroup;
			children = (
				7E5536E4299273DF001CAD68 /* coredata */,
				8E267E8D295B8E8C00CC50BE /* apns */,
				7EB47746292B581100CF1458 /* request */,
				7EB477AF29324BBA00CF1458 /* home */,
				7EB477202925C5AA00CF1458 /* create */,
				7E34541C29223FDB001276CF /* login */,
				7EB477DF293A109600CF1458 /* profile */,
				7EA89289295D832600111D0D /* character */,
				7EBE2F52298504DD0021CD84 /* friend */,
				7EB477D929387DF900CF1458 /* IGAPI.swift */,
				7EB47764292E1CAF00CF1458 /* IGBaseModel.swift */,
			);
			path = Data;
			sourceTree = "<group>";
		};
		7EBDBF58291CAA74008D9AFB /* ViewController */ = {
			isa = PBXGroup;
			children = (
				7E3453FA2921F395001276CF /* PGTabbarController.swift */,
				7EA892722951E1F800111D0D /* IGNavigationViewController.swift */,
				7EA892742951E2B400111D0D /* IGBaseViewController.swift */,
				7EBDBF60291CAD91008D9AFB /* Home */,
				7EA8927B295C2EE900111D0D /* Character */,
				7EBDBF64291CAD91008D9AFB /* CreatImage */,
				7EBDBF65291CAD91008D9AFB /* ImageDetail */,
				7EBDBF66291CAD91008D9AFB /* Login */,
				7EBDBF5F291CAD91008D9AFB /* Menu */,
				7EBDBF62291CAD91008D9AFB /* Profile */,
				7EBDBF75291CCFAC008D9AFB /* Inbox */,
				7EBE2F4D2985015D0021CD84 /* Friend */,
				7E5536FA29AC97A0001CAD68 /* Scribble */,
				8E6E5B98299C567900B099F9 /* Comment */,
				7EB477EA293E1C4B00CF1458 /* UINavigationController_HideNav.swift */,
				8E267E992964B8B300CC50BE /* SwipeNavigationController.swift */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		7EBDBF5F291CAD91008D9AFB /* Menu */ = {
			isa = PBXGroup;
			children = (
				7EBDBF6F291CCF62008D9AFB /* MenuVC.swift */,
				7EB477B229324E2000CF1458 /* MenuHeaderView.swift */,
			);
			path = Menu;
			sourceTree = "<group>";
		};
		7EBDBF60291CAD91008D9AFB /* Home */ = {
			isa = PBXGroup;
			children = (
				7EBDBF67291CADB7008D9AFB /* HomeVC.swift */,
				7EB477902930984D00CF1458 /* view */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		7EBDBF62291CAD91008D9AFB /* Profile */ = {
			isa = PBXGroup;
			children = (
				7EBDBF71291CCF7A008D9AFB /* ProfileVC.swift */,
				7EB477AD29310C8E00CF1458 /* FriendsProfileVC.swift */,
				0724DA672930A98F00899A6B /* view */,
			);
			path = Profile;
			sourceTree = "<group>";
		};
		7EBDBF64291CAD91008D9AFB /* CreatImage */ = {
			isa = PBXGroup;
			children = (
				7EB47776292E573400CF1458 /* ImageCreateDetailVC.swift */,
				7EB4771E2925C44A00CF1458 /* CreatImageVC.swift */,
				7EBE2F5529866CB10021CD84 /* StyleListVC.swift */,
				7E348C442941BB5A005BA9BE /* CICharacterVC.swift */,
				7E5536F629A746FA001CAD68 /* CICharacterNameVC.swift */,
				7EA89294295D9A4600111D0D /* CIPromotionVC.swift */,
				7E5536F829A74D36001CAD68 /* CICharacterLoadingVC.swift */,
				7E348C392941BA2B005BA9BE /* PhotoPicker */,
				7E34542329239F8C001276CF /* view */,
				8E267E90295C2A7A00CC50BE /* JumpToDiscordView.swift */,
			);
			path = CreatImage;
			sourceTree = "<group>";
		};
		7EBDBF65291CAD91008D9AFB /* ImageDetail */ = {
			isa = PBXGroup;
			children = (
				7EB47778292E578F00CF1458 /* ImageDetailVC.swift */,
				7EB47729292736D400CF1458 /* view */,
				8E692E0E29346D9A007D50BF /* viewmodel */,
				8E692DFF292E1259007D50BF /* model */,
				8E692E172937EEB8007D50BF /* utils */,
			);
			path = ImageDetail;
			sourceTree = "<group>";
		};
		7EBDBF66291CAD91008D9AFB /* Login */ = {
			isa = PBXGroup;
			children = (
				8E267E94295E975E00CC50BE /* Utils */,
				8EF824EA2952B53000C7E9FE /* Native */,
				7EBDBF6D291CCF56008D9AFB /* LoginVC.swift */,
				7E97A17A29505A6500397AEA /* LoginWebVC.swift */,
				8E267E92295E547C00CC50BE /* EmailLoginVC.swift */,
				8E267E97295E9BEE00CC50BE /* EmailRegisterVC.swift */,
				8E267EA329667A7C00CC50BE /* ForgetPasswordVC.swift */,
				8E267EA52967B74A00CC50BE /* EnterPinVC.swift */,
				8E267EAA296BDC0B00CC50BE /* ResetPasswordVC.swift */,
			);
			path = Login;
			sourceTree = "<group>";
		};
		7EBDBF75291CCFAC008D9AFB /* Inbox */ = {
			isa = PBXGroup;
			children = (
				7EBDBF76291CD00B008D9AFB /* InboxVC.swift */,
			);
			path = Inbox;
			sourceTree = "<group>";
		};
		7EBE2F4D2985015D0021CD84 /* Friend */ = {
			isa = PBXGroup;
			children = (
				8E456D1D2990ACA300DBBECD /* CustomViews */,
				7EBE2F4E298501B50021CD84 /* FriendVC.swift */,
				7EBE2F50298504AC0021CD84 /* ContactsVC.swift */,
				7E5536D2299117C7001CAD68 /* ContactsGuideVC.swift */,
			);
			path = Friend;
			sourceTree = "<group>";
		};
		7EBE2F52298504DD0021CD84 /* friend */ = {
			isa = PBXGroup;
			children = (
				7EBE2F53298505530021CD84 /* ContactsManager.swift */,
				7E5536E229927169001CAD68 /* ContactModel.swift */,
				7E5536D6299232A4001CAD68 /* Contact.swift */,
			);
			path = friend;
			sourceTree = "<group>";
		};
		8E235F3D29A5988B00942493 /* gif */ = {
			isa = PBXGroup;
			children = (
				8E235F4029A5AEEB00942493 /* character_loading.gif */,
			);
			path = gif;
			sourceTree = "<group>";
		};
		8E267E8D295B8E8C00CC50BE /* apns */ = {
			isa = PBXGroup;
			children = (
				8E267E8E295B97DE00CC50BE /* APNSModel.swift */,
			);
			path = apns;
			sourceTree = "<group>";
		};
		8E267E94295E975E00CC50BE /* Utils */ = {
			isa = PBXGroup;
			children = (
				8E267E95295E976E00CC50BE /* PasswordHash.swift */,
				8E267EB0296DF63E00CC50BE /* Validation.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		8E267EA72968ACD600CC50BE /* model */ = {
			isa = PBXGroup;
			children = (
				8E267EA82968AD7D00CC50BE /* ResetPasswordModel.swift */,
			);
			path = model;
			sourceTree = "<group>";
		};
		8E456D1D2990ACA300DBBECD /* CustomViews */ = {
			isa = PBXGroup;
			children = (
				7E5536D42992322B001CAD68 /* ContactsCell.swift */,
			);
			path = CustomViews;
			sourceTree = "<group>";
		};
		8E692DFF292E1259007D50BF /* model */ = {
			isa = PBXGroup;
			children = (
				8E692E00292E126F007D50BF /* ImageDetailModel.swift */,
			);
			path = model;
			sourceTree = "<group>";
		};
		8E692E0E29346D9A007D50BF /* viewmodel */ = {
			isa = PBXGroup;
			children = (
				8E692E15293481EF007D50BF /* ImageDetailViewModel.swift */,
			);
			path = viewmodel;
			sourceTree = "<group>";
		};
		8E692E172937EEB8007D50BF /* utils */ = {
			isa = PBXGroup;
			children = (
				8E692E182937EEF8007D50BF /* ColorTransform.swift */,
			);
			path = utils;
			sourceTree = "<group>";
		};
		8E6E5B98299C567900B099F9 /* Comment */ = {
			isa = PBXGroup;
			children = (
				8E6E5B9F299C6B1B00B099F9 /* model */,
				8E6E5B99299C568E00B099F9 /* view */,
				7E5536EE299DBB2E001CAD68 /* CommentVC.swift */,
			);
			path = Comment;
			sourceTree = "<group>";
		};
		8E6E5B99299C568E00B099F9 /* view */ = {
			isa = PBXGroup;
			children = (
				7E5536F029A318BA001CAD68 /* CommentListView.swift */,
				7E5536F229A31A10001CAD68 /* CommentCell.swift */,
				7E5536F429A485A4001CAD68 /* SendView.swift */,
			);
			path = view;
			sourceTree = "<group>";
		};
		8E6E5B9F299C6B1B00B099F9 /* model */ = {
			isa = PBXGroup;
			children = (
				8E6E5BA0299C6B3300B099F9 /* CommentModel.swift */,
			);
			path = model;
			sourceTree = "<group>";
		};
		8EF824EA2952B53000C7E9FE /* Native */ = {
			isa = PBXGroup;
			children = (
				8E267EA72968ACD600CC50BE /* model */,
				8EF824ED2952B54B00C7E9FE /* view */,
				8EF824EC2952B54300C7E9FE /* viewmodel */,
			);
			path = Native;
			sourceTree = "<group>";
		};
		8EF824EC2952B54300C7E9FE /* viewmodel */ = {
			isa = PBXGroup;
			children = (
				8EF824F42953CFC300C7E9FE /* SignUpViewModel.swift */,
				8EF824FC29552ED000C7E9FE /* LogInViewModel.swift */,
				8E267E9F2964E78E00CC50BE /* ForgetPasswordViewModel.swift */,
				8E267EAC296D257700CC50BE /* OTPViewModel.swift */,
				8E267EAE296D25D700CC50BE /* ResetPasswordViewModel.swift */,
			);
			path = viewmodel;
			sourceTree = "<group>";
		};
		8EF824ED2952B54B00C7E9FE /* view */ = {
			isa = PBXGroup;
			children = (
				8EF824F02952B5FD00C7E9FE /* LoginView.swift */,
				8EF824F62953E98800C7E9FE /* SignUpView.swift */,
				8EF824F82954E93800C7E9FE /* PasswordView.swift */,
				8E267E9B2964C81F00CC50BE /* ForgetPasswordView.swift */,
				8E267E9D2964C84000CC50BE /* EnterPinView.swift */,
				8E267EA129665BF800CC50BE /* ResetPasswordView.swift */,
			);
			path = view;
			sourceTree = "<group>";
		};
		E0A481F96A41C01DED0F2C36 /* Pods */ = {
			isa = PBXGroup;
			children = (
				4694715416F55B67DBD555F3 /* Pods-aiplayground.debug.xcconfig */,
				5C37A6206362D7B8BB99C45E /* Pods-aiplayground.release.xcconfig */,
				0EAAAB5F046B77EF8DD4E55D /* Pods-Imagine.debug.xcconfig */,
				1A0F6B13338801DBFF6049ED /* Pods-Imagine.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7EBDBF3E291B88A1008D9AFB /* Imagine */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7EBDBF53291B88A2008D9AFB /* Build configuration list for PBXNativeTarget "Imagine" */;
			buildPhases = (
				7C59D35D2D9434E0B6E446B3 /* [CP] Check Pods Manifest.lock */,
				7EBDBF3B291B88A1008D9AFB /* Sources */,
				7EBDBF3C291B88A1008D9AFB /* Frameworks */,
				7EBDBF3D291B88A1008D9AFB /* Resources */,
				852870E65347723033E14DD5 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Imagine;
			productName = aiplayground;
			productReference = 7EBDBF3F291B88A1008D9AFB /* Imagine.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7EBDBF37291B88A1008D9AFB /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1410;
				LastUpgradeCheck = 1410;
				TargetAttributes = {
					7EBDBF3E291B88A1008D9AFB = {
						CreatedOnToolsVersion = 14.1;
					};
				};
			};
			buildConfigurationList = 7EBDBF3A291B88A1008D9AFB /* Build configuration list for PBXProject "Imagine" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 7EBDBF36291B88A1008D9AFB;
			productRefGroup = 7EBDBF40291B88A1008D9AFB /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7EBDBF3E291B88A1008D9AFB /* Imagine */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7EBDBF3D291B88A1008D9AFB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8E235F4129A5AEEC00942493 /* character_loading.gif in Resources */,
				8EF824E7294AD62600C7E9FE /* Poppins-Regular.ttf in Resources */,
				8EF824E9294AD85E00C7E9FE /* Poppins-SemiBold.ttf in Resources */,
				7EA8927A295C0BA700111D0D /* GoogleService-Info.plist in Resources */,
				7EBDBF4F291B88A2008D9AFB /* LaunchScreen.storyboard in Resources */,
				7EBDBF4C291B88A2008D9AFB /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		7C59D35D2D9434E0B6E446B3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Imagine-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		852870E65347723033E14DD5 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Imagine/Pods-Imagine-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Imagine/Pods-Imagine-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Imagine/Pods-Imagine-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7EBDBF3B291B88A1008D9AFB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7EB47758292B8FAC00CF1458 /* config.pb.swift in Sources */,
				7E5536F329A31A10001CAD68 /* CommentCell.swift in Sources */,
				7EBDBF68291CADB7008D9AFB /* HomeVC.swift in Sources */,
				7EA892752951E2B400111D0D /* IGBaseViewController.swift in Sources */,
				7EB477AC2930F6AF00CF1458 /* UIButton_expand.swift in Sources */,
				8E692E192937EEF8007D50BF /* ColorTransform.swift in Sources */,
				7E34542729239FD2001276CF /* CIStyleView.swift in Sources */,
				7E5536E6299273E8001CAD68 /* PersistentContainer.swift in Sources */,
				7EB47748292B592E00CF1458 /* IGRequest.swift in Sources */,
				7E34542E2924B7E0001276CF /* UIDefine.swift in Sources */,
				7EB477D22937611A00CF1458 /* LXMLayoutHeaderFooterProtocol.swift in Sources */,
				7EB47773292E570D00CF1458 /* CWBanner.swift in Sources */,
				8E6E5BA929A463E900B099F9 /* InputNameViewModel.swift in Sources */,
				7EB477E3293A10F600CF1458 /* UserInfoModel.swift in Sources */,
				7EA89286295C33A600111D0D /* CharacterListView.swift in Sources */,
				7EA89288295C33D600111D0D /* CharacterImagesListView.swift in Sources */,
				7E5536F129A318BA001CAD68 /* CommentListView.swift in Sources */,
				7EA8928D295D83A800111D0D /* CharacterListModel.swift in Sources */,
				7E348C422941BA2B005BA9BE /* PhotoPickerView.swift in Sources */,
				8E6E5BAB29A58B7100B099F9 /* WaitingOutputView.swift in Sources */,
				7EB4771F2925C44A00CF1458 /* CreatImageVC.swift in Sources */,
				7E5536D7299232A4001CAD68 /* Contact.swift in Sources */,
				8E267E96295E976E00CC50BE /* PasswordHash.swift in Sources */,
				7EB4775A292B8FAC00CF1458 /* ml.pb.swift in Sources */,
				8E692E01292E126F007D50BF /* ImageDetailModel.swift in Sources */,
				7EB477A829309A8200CF1458 /* HomeFeedCell.swift in Sources */,
				7EB477E1293A10BA00CF1458 /* ProfileManager.swift in Sources */,
				7EB477D82937611A00CF1458 /* CIProfileUserView.swift in Sources */,
				7EB4775E292B8FAC00CF1458 /* character.pb.swift in Sources */,
				7EB477DA29387DF900CF1458 /* IGAPI.swift in Sources */,
				7E348C432941BA2B005BA9BE /* PhotoCellView.swift in Sources */,
				7EB477D32937611A00CF1458 /* CIProfileFriendView.swift in Sources */,
				8E267EA229665BF800CC50BE /* ResetPasswordView.swift in Sources */,
				7EB477222925C87700CF1458 /* CreateImageManager.swift in Sources */,
				7EA892732951E1F800111D0D /* IGNavigationViewController.swift in Sources */,
				7EBDBF6E291CCF56008D9AFB /* LoginVC.swift in Sources */,
				7E3454302924DD2C001276CF /* UIColor_extension.swift in Sources */,
				7E348C412941BA2B005BA9BE /* PhotoPicker.swift in Sources */,
				8E692E06292E1342007D50BF /* FullImageView.swift in Sources */,
				7EB477D42937611A00CF1458 /* CIProfileListCell.swift in Sources */,
				7EB477B329324E2000CF1458 /* MenuHeaderView.swift in Sources */,
				7E5536EF299DBB2E001CAD68 /* CommentVC.swift in Sources */,
				7EBE2F54298505530021CD84 /* ContactsManager.swift in Sources */,
				7E5536F729A746FA001CAD68 /* CICharacterNameVC.swift in Sources */,
				8EF824E32942DA6500C7E9FE /* AddPhotoButton.swift in Sources */,
				8E267E91295C2A7A00CC50BE /* JumpToDiscordView.swift in Sources */,
				7E345420292240DF001276CF /* LoginManager.swift in Sources */,
				7E34542B2923A028001276CF /* CITitleView.swift in Sources */,
				7EA89278295314F500111D0D /* FaceCropper.swift in Sources */,
				7EA8927F295C30B200111D0D /* CharacterImagesVC.swift in Sources */,
				7EB4776C292E56CA00CF1458 /* CIPageControl.swift in Sources */,
				7E5536EB299A2E1D001CAD68 /* HomeNavigationBar.swift in Sources */,
				7E348C402941BA2B005BA9BE /* PhotoPickerViewModel.swift in Sources */,
				7EB477D12937611A00CF1458 /* LXMHeaderFooterFlowLayout.swift in Sources */,
				7EA89293295D85B600111D0D /* CharacterInfoParam.swift in Sources */,
				7EB477D72937611A00CF1458 /* CIProfileView.swift in Sources */,
				7E97A16F2949C35600397AEA /* CICharacterNameModel.swift in Sources */,
				7EB477242926216A00CF1458 /* CIStyleCell.swift in Sources */,
				7E60D2AA2965738A00D48B1A /* String_extension.swift in Sources */,
				8E267E9A2964B8B300CC50BE /* SwipeNavigationController.swift in Sources */,
				7E5536D52992322B001CAD68 /* ContactsCell.swift in Sources */,
				7EB47763292E0AB400CF1458 /* CIGeneratingModel.swift in Sources */,
				8E692E16293481EF007D50BF /* ImageDetailViewModel.swift in Sources */,
				8E267EA62967B74A00CC50BE /* EnterPinVC.swift in Sources */,
				7EB47761292B9B2900CF1458 /* CIGeneratingParam.swift in Sources */,
				7EB477F0293EE5DF00CF1458 /* AmpManager.swift in Sources */,
				8E6E5BA729A460F100B099F9 /* InputNameView.swift in Sources */,
				7EB4774B292B6ECD00CF1458 /* CIStyle.swift in Sources */,
				8E267EA429667A7C00CC50BE /* ForgetPasswordVC.swift in Sources */,
				7EBDBF70291CCF62008D9AFB /* MenuVC.swift in Sources */,
				7EBDBF77291CD00B008D9AFB /* InboxVC.swift in Sources */,
				7E34542929239FF1001276CF /* CINumberView.swift in Sources */,
				7EB4775C292B8FAC00CF1458 /* user.pb.swift in Sources */,
				7EB47779292E578F00CF1458 /* ImageDetailVC.swift in Sources */,
				7E5536FC29AC97D0001CAD68 /* ScribbleVC.swift in Sources */,
				7E97A17B29505A6500397AEA /* LoginWebVC.swift in Sources */,
				7EB47765292E1CAF00CF1458 /* IGBaseModel.swift in Sources */,
				7EB477AA29309BCE00CF1458 /* HomeFeedView.swift in Sources */,
				8E692E04292E12B5007D50BF /* ImageDetailView.swift in Sources */,
				8E267EAB296BDC0B00CC50BE /* ResetPasswordVC.swift in Sources */,
				7E348C4D2941E1B6005BA9BE /* CICharacterListCell.swift in Sources */,
				7E97A1712949C70B00397AEA /* CICharacterMaterialsModel.swift in Sources */,
				7EB4776B292E56CA00CF1458 /* CIStatusView.swift in Sources */,
				7EB47728292710B700CF1458 /* CTPickerView.swift in Sources */,
				7EB47777292E573400CF1458 /* ImageCreateDetailVC.swift in Sources */,
				7E345422292323C9001276CF /* KeyChainManager.swift in Sources */,
				7EBE2F51298504AC0021CD84 /* ContactsVC.swift in Sources */,
				8E267EB1296DF63E00CC50BE /* Validation.swift in Sources */,
				7E5536ED299A5AF0001CAD68 /* UIImage_extension.swift in Sources */,
				8E6E5BA529A44F4700B099F9 /* NewPhotoPickerView.swift in Sources */,
				7EB4775B292B8FAC00CF1458 /* job.pb.swift in Sources */,
				7E348C492941DC9E005BA9BE /* SwiftUIColor.swift in Sources */,
				7EB4776E292E56CA00CF1458 /* CIScrollImageCell.swift in Sources */,
				8E267EA92968AD7D00CC50BE /* ResetPasswordModel.swift in Sources */,
				7EBDBF72291CCF7A008D9AFB /* ProfileVC.swift in Sources */,
				7E5536F929A74D36001CAD68 /* CICharacterLoadingVC.swift in Sources */,
				7EB477AE29310C8E00CF1458 /* FriendsProfileVC.swift in Sources */,
				8E267EAD296D257700CC50BE /* OTPViewModel.swift in Sources */,
				7EB47757292B8FAC00CF1458 /* feeds.pb.swift in Sources */,
				7EA8928B295D835300111D0D /* CharacterManager.swift in Sources */,
				8E6E5BA329A4261800B099F9 /* TutorialView.swift in Sources */,
				8E267EAF296D25D700CC50BE /* ResetPasswordViewModel.swift in Sources */,
				8EF824FD29552ED000C7E9FE /* LogInViewModel.swift in Sources */,
				7E5536F529A485A4001CAD68 /* SendView.swift in Sources */,
				7EB477E7293A511F00CF1458 /* ImageInfoModel.swift in Sources */,
				7EB47759292B8FAC00CF1458 /* prompt.pb.swift in Sources */,
				8E267E98295E9BEE00CC50BE /* EmailRegisterVC.swift in Sources */,
				7EB4776D292E56CA00CF1458 /* CIScrollImageView.swift in Sources */,
				7EBE2F4F298501B50021CD84 /* FriendVC.swift in Sources */,
				8E267E9E2964C84000CC50BE /* EnterPinView.swift in Sources */,
				7E348C472941D81E005BA9BE /* CICharacterListView.swift in Sources */,
				7E5536D3299117C7001CAD68 /* ContactsGuideVC.swift in Sources */,
				7E97A179295046A800397AEA /* LoginModel.swift in Sources */,
				7E348C55294707DD005BA9BE /* CICharacterModel.swift in Sources */,
				7EA89282295C32EB00111D0D /* CharacterCell.swift in Sources */,
				7EA8928F295D83B400111D0D /* CharacterInfoModel.swift in Sources */,
				8E6E5BA1299C6B3300B099F9 /* CommentModel.swift in Sources */,
				7EB477E5293A50BB00CF1458 /* ProfileResultModel.swift in Sources */,
				7E5536E32992716A001CAD68 /* ContactModel.swift in Sources */,
				8E267EA02964E78E00CC50BE /* ForgetPasswordViewModel.swift in Sources */,
				7EBE2F5629866CB10021CD84 /* StyleListVC.swift in Sources */,
				8EF824F52953CFC300C7E9FE /* SignUpViewModel.swift in Sources */,
				7EB477D52937611A00CF1458 /* CIProfileControl.swift in Sources */,
				7EB477B129324BE300CF1458 /* HomeManager.swift in Sources */,
				7E3453FB2921F395001276CF /* PGTabbarController.swift in Sources */,
				8EF824F3295396F900C7E9FE /* ImageModifier.swift in Sources */,
				7EB477EB293E1C4B00CF1458 /* UINavigationController_HideNav.swift in Sources */,
				7EBDBF43291B88A1008D9AFB /* AppDelegate.swift in Sources */,
				8E267E93295E547C00CC50BE /* EmailLoginVC.swift in Sources */,
				7EA8927D295C2F0100111D0D /* CharacterVC.swift in Sources */,
				8E267E9C2964C81F00CC50BE /* ForgetPasswordView.swift in Sources */,
				7EA89295295D9A4600111D0D /* CIPromotionVC.swift in Sources */,
				8EF824F72953E98800C7E9FE /* SignUpView.swift in Sources */,
				7E34542529239FB8001276CF /* CIPromptView.swift in Sources */,
				7EA89291295D84F100111D0D /* CharacterListParam.swift in Sources */,
				8EF824F12952B5FD00C7E9FE /* LoginView.swift in Sources */,
				7E97A16D294886B800397AEA /* CIRandomPromptModel.swift in Sources */,
				7EB477EF293EE5DF00CF1458 /* AmpConstants.swift in Sources */,
				7EB4775D292B8FAC00CF1458 /* model.pb.swift in Sources */,
				7EB477DC2938B24B00CF1458 /* HomeFeedModel.swift in Sources */,
				8EB8864429B706E1006FC905 /* DraftView.swift in Sources */,
				7EB477E9293A53A800CF1458 /* ImageListParam.swift in Sources */,
				7EB47775292E570D00CF1458 /* CWProxy.swift in Sources */,
				7E5536E929928C86001CAD68 /* imagine.xcdatamodeld in Sources */,
				7EB477D62937611A00CF1458 /* CIProfileHeaderView.swift in Sources */,
				8EF824F92954E93800C7E9FE /* PasswordView.swift in Sources */,
				7EB47756292B8FAC00CF1458 /* generate.pb.swift in Sources */,
				8E267E8F295B97DE00CC50BE /* APNSModel.swift in Sources */,
				7E348C452941BB5A005BA9BE /* CICharacterVC.swift in Sources */,
				7EA892712951A84E00111D0D /* UIViewController_extension.swift in Sources */,
				8E235F4329A5B49600942493 /* TimerView.swift in Sources */,
				7EB47774292E570D00CF1458 /* CWSwiftFlowLayout.swift in Sources */,
				7EA89284295C32FB00111D0D /* CharacterImagesCell.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		7EBDBF4D291B88A2008D9AFB /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				7EBDBF4E291B88A2008D9AFB /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		7EBDBF51291B88A2008D9AFB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		7EBDBF52291B88A2008D9AFB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7EBDBF54291B88A2008D9AFB /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0EAAAB5F046B77EF8DD4E55D /* Pods-Imagine.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = aiplayground/aiplayground.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 5;
				DEVELOPMENT_TEAM = WS7AN4RH68;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/aiplayground/View/TabarContenView/Lottie",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = aiplayground/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Imagine;
				INFOPLIST_KEY_NSContactsUsageDescription = "Do you allow this App to access your address book?";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "The app requires your consent to access the album in order to save the pictures";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "The APP requires your consent to access the album, so as to facilitate photo selection, upload and release";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen.storyboard;
				INFOPLIST_KEY_UIStatusBarHidden = YES;
				INFOPLIST_KEY_UIStatusBarStyle = UIStatusBarStyleDarkContent;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 0.03;
				PRODUCT_BUNDLE_IDENTIFIER = com.8glabs.imagineapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		7EBDBF55291B88A2008D9AFB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1A0F6B13338801DBFF6049ED /* Pods-Imagine.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = aiplayground/aiplayground.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 5;
				DEVELOPMENT_TEAM = WS7AN4RH68;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/aiplayground/View/TabarContenView/Lottie",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = aiplayground/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Imagine;
				INFOPLIST_KEY_NSContactsUsageDescription = "Do you allow this App to access your address book?";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "The app requires your consent to access the album in order to save the pictures";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "The APP requires your consent to access the album, so as to facilitate photo selection, upload and release";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen.storyboard;
				INFOPLIST_KEY_UIStatusBarHidden = YES;
				INFOPLIST_KEY_UIStatusBarStyle = UIStatusBarStyleDarkContent;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 0.03;
				PRODUCT_BUNDLE_IDENTIFIER = com.8glabs.imagineapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7EBDBF3A291B88A1008D9AFB /* Build configuration list for PBXProject "Imagine" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7EBDBF51291B88A2008D9AFB /* Debug */,
				7EBDBF52291B88A2008D9AFB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7EBDBF53291B88A2008D9AFB /* Build configuration list for PBXNativeTarget "Imagine" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7EBDBF54291B88A2008D9AFB /* Debug */,
				7EBDBF55291B88A2008D9AFB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCVersionGroup section */
		7E5536E729928C86001CAD68 /* imagine.xcdatamodeld */ = {
			isa = XCVersionGroup;
			children = (
				7E5536E829928C86001CAD68 /* imagine.xcdatamodel */,
			);
			currentVersion = 7E5536E829928C86001CAD68 /* imagine.xcdatamodel */;
			path = imagine.xcdatamodeld;
			sourceTree = "<group>";
			versionGroupType = wrapper.xcdatamodel;
		};
/* End XCVersionGroup section */
	};
	rootObject = 7EBDBF37291B88A1008D9AFB /* Project object */;
}
