<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "0F97D930-64CD-431E-80CB-1F2282BBF063"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D2BAA843-54A5-4CD6-9AF0-B0E067EC5BC0"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "aiplayground/ViewController/Login/Native/view/EnterPinView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "135"
            endingLineNumber = "135"
            landmarkName = "getImageName(at:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "03D1FF63-0DB3-48A5-AC7D-4FE1102D0CCA"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "aiplayground/ViewController/Login/ResetPasswordVC.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "17"
            endingLineNumber = "17"
            landmarkName = "init(token:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "22D2A833-63ED-4544-9593-1BDB8CCADEB7"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "aiplayground/ViewController/Login/Native/viewmodel/OTPViewModel.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "31"
            endingLineNumber = "31"
            landmarkName = "OTPViewModel"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
