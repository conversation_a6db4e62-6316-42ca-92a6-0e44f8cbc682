/*! @file OIDClientMetadataParameters.h
    @brief AppAuth iOS SDK
    @copyright
        Copyright 2016 The AppAuth for iOS Authors. All Rights Reserved.
    @copydetails
        Licensed under the Apache License, Version 2.0 (the "License");
        you may not use this file except in compliance with the License.
        You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

        Unless required by applicable law or agreed to in writing, software
        distributed under the License is distributed on an "AS IS" BASIS,
        WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
        See the License for the specific language governing permissions and
        limitations under the License.
 */

#import "OIDClientMetadataParameters.h"

NSString *const OIDTokenEndpointAuthenticationMethodParam = @"token_endpoint_auth_method";

NSString *const OIDApplicationTypeParam = @"application_type";

NSString *const OIDRedirectURIsParam = @"redirect_uris";

NSString *const OIDResponseTypesParam = @"response_types";

NSString *const OIDGrantTypesParam = @"grant_types";

NSString *const OIDSubjectTypeParam = @"subject_type";

NSString *const OIDApplicationTypeNative = @"native";
