<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>Alamofire.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>9</integer>
		</dict>
		<key>Amplitude.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>11</integer>
		</dict>
		<key>AnalyticsConnector.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>19</integer>
		</dict>
		<key>AppAuth.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>28</integer>
		</dict>
		<key>CryptoSwift.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>31</integer>
		</dict>
		<key>DeviceKit.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>2</integer>
		</dict>
		<key>ESPullToRefresh-ESPullToRefresh.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>29</integer>
		</dict>
		<key>ESPullToRefresh.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>22</integer>
		</dict>
		<key>FirebaseAnalytics.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>35</integer>
		</dict>
		<key>FirebaseCore.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>18</integer>
		</dict>
		<key>FirebaseCoreInternal.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>15</integer>
		</dict>
		<key>FirebaseInstallations.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>30</integer>
		</dict>
		<key>FirebaseMessaging.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>5</integer>
		</dict>
		<key>GTMAppAuth.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>21</integer>
		</dict>
		<key>GTMSessionFetcher.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>20</integer>
		</dict>
		<key>GoogleAppMeasurement.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>10</integer>
		</dict>
		<key>GoogleDataTransport.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>33</integer>
		</dict>
		<key>GoogleSignIn-GoogleSignIn.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>6</integer>
		</dict>
		<key>GoogleSignIn.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>12</integer>
		</dict>
		<key>GoogleUtilities.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>3</integer>
		</dict>
		<key>HandyJSON.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>23</integer>
		</dict>
		<key>KMPlaceholderTextView.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>34</integer>
		</dict>
		<key>KeychainSwift.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>25</integer>
		</dict>
		<key>MJRefresh.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>7</integer>
		</dict>
		<key>NVActivityIndicatorView.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>14</integer>
		</dict>
		<key>Pods-Imagine.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>4</integer>
		</dict>
		<key>PromisesObjC.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>13</integer>
		</dict>
		<key>SDWebImage.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>17</integer>
		</dict>
		<key>SDWebImageSwiftUI.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>8</integer>
		</dict>
		<key>SnapKit.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>26</integer>
		</dict>
		<key>SwiftProtobuf.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>24</integer>
		</dict>
		<key>SwiftyDraw.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>32</integer>
		</dict>
		<key>Toast-Swift.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>27</integer>
		</dict>
		<key>nanopb.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>16</integer>
		</dict>
	</dict>
</dict>
</plist>
